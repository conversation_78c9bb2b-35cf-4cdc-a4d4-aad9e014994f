# SEO自动化检查工具 - 使用指南

## 🎯 工具简介

这是一个专为测试工程师设计的SEO自动化检查工具，可以快速分析网站的SEO状况并生成专业的HTML报告。

## 🚀 快速开始

### 1. 最简单的使用方式
```bash
# 检查单个网站
python3 main.py https://www.yourcompany.com

# 检查并指定报告文件名
python3 main.py https://www.yourcompany.com -o company_seo_report.html

# 显示详细输出
python3 main.py https://www.yourcompany.com -v
```

### 2. 快速检查模式
```bash
# 快速检查（提供简化的结果）
python3 quick_check.py https://www.yourcompany.com

# 或者运行交互式模式
python3 quick_check.py
```

### 3. 示例和批量检查
```bash
# 运行示例脚本（包含批量检查功能）
python3 example_usage.py
```

## 📁 报告文件管理

### 报告存储位置
所有生成的SEO报告都会自动保存在 `reports` 目录下：

```
smind/
├── reports/                    # 报告目录
│   ├── static/                # 报告样式文件
│   │   └── style.css          # CSS样式
│   ├── seo_report.html        # 默认报告文件
│   ├── company_seo_report.html # 自定义命名的报告
│   └── quick_seo_report_*.html # 快速检查报告
├── main.py
├── quick_check.py
└── ...
```

### 报告文件特点
- **自动创建目录**: 首次运行时会自动创建 `reports` 目录
- **独立样式文件**: 每个报告都有独立的CSS样式文件
- **时间戳命名**: 快速检查会自动添加时间戳避免文件覆盖
- **相对路径**: 报告中的CSS引用使用相对路径，便于移动和分享

## 📊 报告解读

### 评分等级
- **A级 (90-100分)**: 🎉 优秀 - SEO表现很好
- **B级 (80-89分)**: 👍 良好 - 还有一些改进空间
- **C级 (70-79分)**: ⚠️ 一般 - 建议进行优化
- **D级 (60-69分)**: 🔧 需要改进 - 存在多个SEO问题
- **F级 (0-59分)**: 🚨 较差 - 急需SEO优化

### 检查项目说明

#### 🔍 基础SEO检查
- **页面标题**: 检查标题长度(30-60字符)和内容质量
- **Meta描述**: 验证描述存在性和长度(120-160字符)
- **标题结构**: H1-H6标签的正确使用
- **图片优化**: Alt属性完整性检查
- **Meta关键词**: 关键词标签检查

#### ⚙️ 技术SEO检查
- **Robots.txt**: 检查robots.txt文件存在性
- **XML网站地图**: 验证sitemap.xml可访问性
- **URL结构**: 友好URL结构分析
- **内部链接**: 内部链接数量和质量
- **外部链接**: 外部链接统计

#### 🚀 性能检查
- **页面加载时间**: 测量页面响应时间
  - 优秀: ≤2秒
  - 良好: 2-4秒
  - 一般: 4-6秒
  - 较差: >6秒
- **页面大小**: 检查页面资源大小
  - 优秀: ≤500KB
  - 良好: 500KB-1MB
  - 一般: 1-2MB
  - 较差: >2MB

#### 📱 移动端友好性
- **Viewport设置**: 移动端viewport配置检查
- **响应式设计**: 媒体查询检测

#### 🔒 安全检查
- **HTTPS检查**: SSL证书和加密连接验证

#### 📝 内容分析
- **内容长度**: 页面内容词数统计
  - 最少: 300词
  - 最佳: 1000词以上

## 🛠️ 自定义配置

### 修改公司信息
编辑 `config.py` 文件中的报告配置：
```python
REPORT_CONFIG = {
    'title': 'SEO审计报告',
    'company': '您的公司名称',  # 修改这里
    'logo_url': '',  # 可选：添加公司logo URL
    'include_recommendations': True,
    'include_technical_details': True
}
```

### 调整评分权重
在 `config.py` 中修改各项检查的权重：
```python
SCORE_WEIGHTS = {
    'title': 15,           # 页面标题权重
    'meta_description': 10, # 元描述权重
    'headings': 10,        # 标题结构权重
    'images': 10,          # 图片优化权重
    'links': 10,           # 链接分析权重
    'page_speed': 15,      # 页面速度权重
    'mobile_friendly': 10, # 移动端友好权重
    'ssl': 5,              # SSL证书权重
    'content_length': 5,   # 内容长度权重
    'meta_keywords': 5,    # 关键词权重
    'robots_txt': 5        # robots.txt权重
}
```

## 📋 常见使用场景

### 1. 日常网站监控
```bash
# 每日检查公司官网
python3 quick_check.py https://www.yourcompany.com
```

### 2. 新网站上线前检查
```bash
# 全面检查新网站
python3 main.py https://new-website.com -o pre_launch_seo_audit.html -v
```

### 3. 竞争对手分析
```bash
# 批量检查竞争对手网站
python3 example_usage.py
# 然后选择批量检查模式
```

### 4. SEO优化效果验证
```bash
# 优化前检查
python3 main.py https://www.yoursite.com -o before_optimization.html

# 优化后检查
python3 main.py https://www.yoursite.com -o after_optimization.html
```

## 🔧 故障排除

### 常见问题

1. **检查失败，提示无法访问网站**
   - 检查网站URL是否正确
   - 确认网站可以正常访问
   - 检查网络连接

2. **某些检查项目显示错误**
   - 网站响应时间过长
   - 网站返回错误状态码
   - 网站使用了防爬虫机制

3. **依赖安装失败**
   ```bash
   # 重新安装依赖
   pip3 install -r requirements.txt
   ```

### 获取详细错误信息
使用 `-v` 参数获取详细输出：
```bash
python3 main.py https://problem-site.com -v
```

## 📈 最佳实践

### 1. 定期检查
- 建议每周检查一次主要页面
- 新内容发布后及时检查
- 网站改版后进行全面检查

### 2. 重点关注项目
- 页面标题和描述（影响点击率）
- 页面加载速度（影响用户体验）
- 移动端友好性（移动优先索引）
- HTTPS安全性（搜索引擎要求）

### 3. 报告使用
- 保存历史报告进行对比
- 与开发团队分享技术问题
- 与内容团队分享内容建议

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看本使用指南
2. 检查 `README.md` 文件
3. 使用 `-v` 参数获取详细错误信息
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
