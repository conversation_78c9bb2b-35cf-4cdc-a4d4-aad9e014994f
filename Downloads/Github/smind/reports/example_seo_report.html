
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://example.com</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://example.com</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-26 09:49:54
            </div>
        </div>

        <!-- 总分概览 -->
        
        <div class="score-overview">
            <div class="score-circle score-f">
                59.8
            </div>
            <h2>总体评分: F</h2>
            <p>得分: 59.8 / 100</p>
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: Example Domain<br>
                            长度: 14 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太短，建议至少30个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        46.666666666666664
                    </div>
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: 无<br>
                            长度: 0 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>页面缺少meta描述</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        0
                    </div>
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 1 个
                            
                            <br>内容: Example Domain
                            
                            <br>
                            
                            H2: 0 个
                            
                            <br>
                            
                            H3: 0 个
                            
                            <br>
                            
                            H4: 0 个
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 0<br>
                            缺少alt属性: 0
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100.0
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加robots.txt文件</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-average">
                        50
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 否
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加XML网站地图</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-average">
                        50
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 0
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>增加内部链接以改善网站结构</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        0
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.76 秒
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 1.23 KB
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width, initial-scale=1
                            
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 是
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 30
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>内容太少，建议至少300个词</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        5.0
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 09:49:54</p>
        </div>
    </div>
</body>
</html>
        