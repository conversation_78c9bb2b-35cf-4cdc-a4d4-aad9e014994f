
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO审计报告 - https://www.w3.org</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>SEO审计报告</h1>
            <div class="url">https://www.w3.org</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: 2025-05-26 14:15:16
            </div>
        </div>

        <!-- 总分概览 -->
        
        <div class="score-overview">
            <div class="score-circle score-d">
                66.8
            </div>
            <h2>总体评分: D</h2>
            <p>得分: 66.8 / 100</p>
        </div>
        

        <!-- 基础SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: W3C<br>
                            长度: 3 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>标题太短，建议至少30个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        10.0
                    </div>
                </div>
                

                <!-- Meta描述检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: The World Wide Web Consortium (W3C) develops standards and guidelines to help everyone build a web based on the principles of accessibility, internationalization, privacy and security.<br>
                            长度: 184 字符
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>描述太长，建议不超过160个字符</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-average">
                        52
                    </div>
                </div>
                

                <!-- 标题结构检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            
                            H1: 1 个
                            
                            <br>内容: Making the web work
                            
                            <br>
                            
                            H2: 11 个
                            
                            <br>内容: Standards, Groups, Get involved...
                            
                            <br>
                            
                            H3: 4 个
                            
                            <br>内容: New Privacy Principles for a more trustworthy web, Privacy Principles is a W3C Statement, First Public Working Draft: CSS Functions and Mixins Module...
                            
                            <br>
                            
                            H4: 0 个
                            
                            <br>
                            
                            H5: 0 个
                            
                            <br>
                            
                            H6: 0 个
                            
                            <br>
                            
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 图片检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: 9<br>
                            缺少alt属性: 8
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>8张图片缺少alt属性</li>
                                
                            </ul>
                        </div>
                        

                        <!-- 显示缺少ALT属性的图片详情 -->
                        
                        <div class="missing-alt-images">
                            <h4>缺少ALT属性的图片:</h4>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/assets/website-2021/svg/search.svg" target="_blank" class="image-link">https://www.w3.org/assets/website-2021/svg/search.svg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/assets/website-2021/svg/search.svg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/assets/website-2021/svg/avatar.svg" target="_blank" class="image-link">https://www.w3.org/assets/website-2021/svg/avatar.svg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/assets/website-2021/svg/avatar.svg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/cms-uploads/Hero-illustrations/groups.svg" target="_blank" class="image-link">https://www.w3.org/cms-uploads/Hero-illustrations/groups.svg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/cms-uploads/Hero-illustrations/groups.svg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/cms-uploads/w30c-pattern.jpeg" target="_blank" class="image-link">https://www.w3.org/cms-uploads/w30c-pattern.jpeg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/cms-uploads/w30c-pattern.jpeg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/cms-uploads/ryan-quintal-US9Tc9pKNBU-unsplash.jpg" target="_blank" class="image-link">https://www.w3.org/cms-uploads/ryan-quintal-US9Tc9pKNBU-unsplash.jpg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/cms-uploads/ryan-quintal-US9Tc9pKNBU-unsplash.jpg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/cms-uploads/marvin-meyer-SYTO3xs06fU-unsplash.jpg" target="_blank" class="image-link">https://www.w3.org/cms-uploads/marvin-meyer-SYTO3xs06fU-unsplash.jpg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/cms-uploads/marvin-meyer-SYTO3xs06fU-unsplash.jpg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/assets/website-2021/svg/mastodon.svg" target="_blank" class="image-link">https://www.w3.org/assets/website-2021/svg/mastodon.svg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/assets/website-2021/svg/mastodon.svg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="missing-alt-item">
                                <div class="alt-status">
                                    <span class="alt-label">Alt</span>
                                    <span class="alt-missing">⚠ Missing</span>
                                </div>
                                <div class="image-info">
                                    <div class="image-url">
                                        <strong>URL:</strong>
                                        <a href="https://www.w3.org/assets/website-2021/svg/github.svg" target="_blank" class="image-link">https://www.w3.org/assets/website-2021/svg/github.svg</a>
                                    </div>
                                    <div class="image-preview">
                                        <img src="https://www.w3.org/assets/website-2021/svg/github.svg" alt="预览图" class="preview-img"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div class="preview-error" style="display:none;">
                                            <span>🖼️ 图片无法加载</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                        
                    </div>
                    <div class="check-score score-poor">
                        11.111111111111114
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 技术SEO -->
        
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: 是
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- Sitemap检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: 否
                            
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加XML网站地图</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-average">
                        50
                    </div>
                </div>
                

                <!-- 内部链接检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: 96
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 性能检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: 0.2 秒
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 页面大小检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: 48.8 KB
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 移动端友好性 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: 是
                            
                            <br>内容: width=device-width, initial-scale=1
                            
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                

                <!-- 响应式设计检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: 否
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议添加响应式设计支持</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-average">
                        50
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 安全检查 -->
        
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: 是
                        </div>
                        
                    </div>
                    <div class="check-score score-excellent">
                        100
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 内容分析 -->
        
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: 804
                        </div>
                        
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                
                                <li>建议增加内容到1000个词以上</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    <div class="check-score score-good">
                        80.4
                    </div>
                </div>
                
            </div>
        </div>
        

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 测试工程师团队 SEO审计工具生成</p>
            <p>生成时间: 2025-05-26 14:15:16</p>
        </div>
    </div>
</body>
</html>
        