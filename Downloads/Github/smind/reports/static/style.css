
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .score-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            font-weight: bold;
            color: white;
        }

        .score-a { background: #4CAF50; }
        .score-b { background: #8BC34A; }
        .score-c { background: #FFC107; }
        .score-d { background: #FF9800; }
        .score-f { background: #F44336; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .check-name {
            font-weight: 500;
            flex: 1;
        }

        .check-score {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .score-excellent { background: #4CAF50; }
        .score-good { background: #8BC34A; }
        .score-average { background: #FFC107; }
        .score-poor { background: #FF9800; }
        .score-bad { background: #F44336; }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        /* 缺少ALT属性的图片样式 */
        .missing-alt-images {
            background: #fff8e1;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .missing-alt-images h4 {
            color: #e65100;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .missing-alt-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .missing-alt-item:last-child {
            margin-bottom: 0;
        }

        .alt-status {
            background: #f5f5f5;
            padding: 10px 15px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alt-label {
            font-weight: bold;
            color: #666;
            min-width: 30px;
        }

        .alt-missing {
            color: #ff9800;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .image-info {
            padding: 15px;
        }

        .image-url {
            margin-bottom: 15px;
            word-break: break-all;
        }

        .image-link {
            color: #1976d2;
            text-decoration: none;
            font-family: monospace;
            font-size: 0.9em;
        }

        .image-link:hover {
            text-decoration: underline;
        }

        .image-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
        }

        .preview-img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preview-error {
            color: #666;
            font-style: italic;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        