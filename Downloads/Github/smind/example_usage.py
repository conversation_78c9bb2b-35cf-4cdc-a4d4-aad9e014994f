#!/usr/bin/env python3
"""
SEO检查工具使用示例
演示如何使用SEO检查工具检查不同类型的网站
"""

import os
import sys
from datetime import datetime
from seo_checker import SEOChecker
from report_generator import ReportGenerator

def check_website(url, output_filename=None):
    """检查单个网站"""
    print(f"\n{'='*60}")
    print(f"🔍 开始检查网站: {url}")
    print(f"{'='*60}")

    try:
        # 创建SEO检查器
        checker = SEOChecker(url)

        # 执行检查
        results = checker.check_all()

        if results is None:
            print("❌ 检查失败: 无法获取网站内容")
            return False

        # 显示简要结果
        if 'total_score' in results:
            score = results['total_score']['score']
            grade = results['total_score']['grade']
            print(f"📊 总体评分: {score} ({grade})")

        # 生成报告文件名（将自动保存到reports目录下）
        if not output_filename:
            domain = url.replace('https://', '').replace('http://', '').replace('/', '_')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_filename = f"seo_report_{domain}_{timestamp}.html"

        # 生成报告
        generator = ReportGenerator()
        output_file = generator.generate_html_report(results, url, output_filename)

        print(f"✅ 报告已生成: {output_file}")
        return True

    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def batch_check_websites(urls):
    """批量检查多个网站"""
    print(f"\n🚀 开始批量检查 {len(urls)} 个网站")

    results = []
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] 检查网站: {url}")
        success = check_website(url)
        results.append((url, success))

    # 显示汇总结果
    print(f"\n{'='*60}")
    print("📋 批量检查结果汇总:")
    print(f"{'='*60}")

    successful = sum(1 for _, success in results if success)
    failed = len(results) - successful

    for url, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {status} - {url}")

    print(f"\n📊 统计:")
    print(f"  成功: {successful}")
    print(f"  失败: {failed}")
    print(f"  总计: {len(results)}")

def main():
    """主函数 - 演示不同的使用方式"""

    print("🔍 SEO自动化检查工具 - 使用示例")
    print("="*60)

    # 示例1: 检查单个网站
    print("\n📝 示例1: 检查单个网站")
    check_website("https://example.com", "example_seo_report.html")

    # 示例2: 检查多个知名网站
    print("\n📝 示例2: 批量检查多个网站")
    test_websites = [
        "https://www.google.com",
        "https://www.github.com",
        "https://www.stackoverflow.com"
    ]

    # 询问用户是否要执行批量检查
    response = input("\n是否要执行批量检查示例？(y/n): ").lower().strip()
    if response == 'y' or response == 'yes':
        batch_check_websites(test_websites)
    else:
        print("跳过批量检查示例")

    # 示例3: 交互式检查
    print("\n📝 示例3: 交互式检查")
    while True:
        url = input("\n请输入要检查的网站URL (输入 'quit' 退出): ").strip()

        if url.lower() in ['quit', 'exit', 'q']:
            break

        if not url:
            print("请输入有效的URL")
            continue

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        check_website(url)

    print("\n👋 感谢使用SEO自动化检查工具!")

if __name__ == "__main__":
    main()
