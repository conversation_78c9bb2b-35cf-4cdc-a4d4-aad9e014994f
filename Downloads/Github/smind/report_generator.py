import os
from datetime import datetime
from jinja2 import Template
import config

class ReportGenerator:
    def __init__(self):
        self.template_dir = 'templates'
        self.static_dir = 'static'
        self.reports_dir = 'reports'

    def generate_html_report(self, seo_results, url, output_file='seo_report.html'):
        """生成HTML报告"""

        # 创建报告目录结构
        reports_dir = self.reports_dir
        reports_static_dir = os.path.join(reports_dir, 'static')

        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        os.makedirs(self.static_dir, exist_ok=True)
        os.makedirs(reports_dir, exist_ok=True)
        os.makedirs(reports_static_dir, exist_ok=True)

        # 创建CSS样式文件（在reports/static目录下）
        self._create_css_file(reports_static_dir)

        # 如果output_file没有包含路径，则放到reports目录下
        if not os.path.dirname(output_file):
            output_file = os.path.join(reports_dir, output_file)

        # 创建HTML模板
        template_content = self._get_html_template()
        template = Template(template_content)

        # 准备模板数据
        template_data = {
            'url': url,
            'results': seo_results,
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'config': config.REPORT_CONFIG
        }

        # 渲染HTML
        html_content = template.render(**template_data)

        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"报告已生成: {output_file}")
        return output_file

    def _create_css_file(self, target_dir=None):
        """创建CSS样式文件"""
        if target_dir is None:
            target_dir = self.static_dir
        css_content = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .url {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .score-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            font-weight: bold;
            color: white;
        }

        .score-a { background: #4CAF50; }
        .score-b { background: #8BC34A; }
        .score-c { background: #FFC107; }
        .score-d { background: #FF9800; }
        .score-f { background: #F44336; }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }

        .section-content {
            padding: 20px;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .check-item:last-child {
            border-bottom: none;
        }

        .check-name {
            font-weight: 500;
            flex: 1;
        }

        .check-score {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-left: 10px;
        }

        .score-excellent { background: #4CAF50; }
        .score-good { background: #8BC34A; }
        .score-average { background: #FFC107; }
        .score-poor { background: #FF9800; }
        .score-bad { background: #F44336; }

        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }

        .recommendations h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .recommendations ul {
            list-style-type: none;
            padding-left: 0;
        }

        .recommendations li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .recommendations li:before {
            content: "⚠";
            position: absolute;
            left: 0;
            color: #856404;
        }

        .details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .score-circle {
                width: 120px;
                height: 120px;
                font-size: 2em;
            }

            .check-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .check-score {
                margin-left: 0;
                margin-top: 10px;
            }
        }
        """

        css_file = os.path.join(target_dir, 'style.css')
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(css_content)

    def _get_html_template(self):
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config.title }} - {{ url }}</title>
    <link rel="stylesheet" href="static/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>{{ config.title }}</h1>
            <div class="url">{{ url }}</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                生成时间: {{ report_date }}
            </div>
        </div>

        <!-- 总分概览 -->
        {% if results.total_score %}
        <div class="score-overview">
            <div class="score-circle score-{{ results.total_score.grade.lower() }}">
                {{ results.total_score.score }}
            </div>
            <h2>总体评分: {{ results.total_score.grade }}</h2>
            <p>得分: {{ results.total_score.achieved }} / {{ results.total_score.max_possible }}</p>
        </div>
        {% endif %}

        <!-- 基础SEO -->
        {% if results.basic_seo %}
        <div class="section">
            <div class="section-header">
                <h2>🔍 基础SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- 标题检查 -->
                {% if results.basic_seo.title %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面标题</strong>
                        <div class="details">
                            内容: {{ results.basic_seo.title.content or '无' }}<br>
                            长度: {{ results.basic_seo.title.length }} 字符
                        </div>
                        {% if results.basic_seo.title.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.title.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.basic_seo.title.score >= 90 else 'score-good' if results.basic_seo.title.score >= 70 else 'score-average' if results.basic_seo.title.score >= 50 else 'score-poor' }}">
                        {{ results.basic_seo.title.score }}
                    </div>
                </div>
                {% endif %}

                <!-- Meta描述检查 -->
                {% if results.basic_seo.meta_description %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Meta描述</strong>
                        <div class="details">
                            内容: {{ results.basic_seo.meta_description.content or '无' }}<br>
                            长度: {{ results.basic_seo.meta_description.length }} 字符
                        </div>
                        {% if results.basic_seo.meta_description.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.meta_description.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.basic_seo.meta_description.score >= 90 else 'score-good' if results.basic_seo.meta_description.score >= 70 else 'score-average' if results.basic_seo.meta_description.score >= 50 else 'score-poor' }}">
                        {{ results.basic_seo.meta_description.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 标题结构检查 -->
                {% if results.basic_seo.headings %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>标题结构 (H1-H6)</strong>
                        <div class="details">
                            {% for heading, data in results.basic_seo.headings.items() %}
                            {{ heading.upper() }}: {{ data.count }} 个
                            {% if data.content %}
                            <br>内容: {{ data.content[:3]|join(', ') }}{% if data.content|length > 3 %}...{% endif %}
                            {% endif %}
                            <br>
                            {% endfor %}
                        </div>
                        {% if results.basic_seo.headings.h1.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.headings.h1.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.basic_seo.headings.h1.score >= 90 else 'score-good' if results.basic_seo.headings.h1.score >= 70 else 'score-average' if results.basic_seo.headings.h1.score >= 50 else 'score-poor' }}">
                        {{ results.basic_seo.headings.h1.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 图片检查 -->
                {% if results.basic_seo.images %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>图片优化</strong>
                        <div class="details">
                            总图片数: {{ results.basic_seo.images.total }}<br>
                            缺少alt属性: {{ results.basic_seo.images.without_alt }}
                        </div>
                        {% if results.basic_seo.images.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.basic_seo.images.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.basic_seo.images.score >= 90 else 'score-good' if results.basic_seo.images.score >= 70 else 'score-average' if results.basic_seo.images.score >= 50 else 'score-poor' }}">
                        {{ results.basic_seo.images.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 技术SEO -->
        {% if results.technical_seo %}
        <div class="section">
            <div class="section-header">
                <h2>⚙️ 技术SEO检查</h2>
            </div>
            <div class="section-content">
                <!-- Robots.txt检查 -->
                {% if results.technical_seo.robots_txt %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Robots.txt</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.robots_txt.exists else '否' }}
                        </div>
                        {% if results.technical_seo.robots_txt.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.robots_txt.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.technical_seo.robots_txt.score >= 90 else 'score-good' if results.technical_seo.robots_txt.score >= 70 else 'score-average' if results.technical_seo.robots_txt.score >= 50 else 'score-poor' }}">
                        {{ results.technical_seo.robots_txt.score }}
                    </div>
                </div>
                {% endif %}

                <!-- Sitemap检查 -->
                {% if results.technical_seo.sitemap %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>XML网站地图</strong>
                        <div class="details">
                            存在: {{ '是' if results.technical_seo.sitemap.exists else '否' }}
                            {% if results.technical_seo.sitemap.url %}
                            <br>URL: {{ results.technical_seo.sitemap.url }}
                            {% endif %}
                        </div>
                        {% if results.technical_seo.sitemap.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.sitemap.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.technical_seo.sitemap.score >= 90 else 'score-good' if results.technical_seo.sitemap.score >= 70 else 'score-average' if results.technical_seo.sitemap.score >= 50 else 'score-poor' }}">
                        {{ results.technical_seo.sitemap.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 内部链接检查 -->
                {% if results.technical_seo.internal_links %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内部链接</strong>
                        <div class="details">
                            数量: {{ results.technical_seo.internal_links.count }}
                        </div>
                        {% if results.technical_seo.internal_links.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.technical_seo.internal_links.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.technical_seo.internal_links.score >= 90 else 'score-good' if results.technical_seo.internal_links.score >= 70 else 'score-average' if results.technical_seo.internal_links.score >= 50 else 'score-poor' }}">
                        {{ results.technical_seo.internal_links.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 性能检查 -->
        {% if results.performance %}
        <div class="section">
            <div class="section-header">
                <h2>🚀 性能检查</h2>
            </div>
            <div class="section-content">
                <!-- 加载时间检查 -->
                {% if results.performance.load_time %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面加载时间</strong>
                        <div class="details">
                            时间: {{ results.performance.load_time.time }} 秒
                        </div>
                        {% if results.performance.load_time.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.load_time.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.performance.load_time.score >= 90 else 'score-good' if results.performance.load_time.score >= 70 else 'score-average' if results.performance.load_time.score >= 50 else 'score-poor' }}">
                        {{ results.performance.load_time.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 页面大小检查 -->
                {% if results.performance.page_size %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>页面大小</strong>
                        <div class="details">
                            大小: {{ results.performance.page_size.size }} KB
                        </div>
                        {% if results.performance.page_size.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.performance.page_size.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.performance.page_size.score >= 90 else 'score-good' if results.performance.page_size.score >= 70 else 'score-average' if results.performance.page_size.score >= 50 else 'score-poor' }}">
                        {{ results.performance.page_size.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 移动端友好性 -->
        {% if results.mobile %}
        <div class="section">
            <div class="section-header">
                <h2>📱 移动端友好性</h2>
            </div>
            <div class="section-content">
                <!-- Viewport检查 -->
                {% if results.mobile.viewport %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>Viewport设置</strong>
                        <div class="details">
                            存在: {{ '是' if results.mobile.viewport.exists else '否' }}
                            {% if results.mobile.viewport.content %}
                            <br>内容: {{ results.mobile.viewport.content }}
                            {% endif %}
                        </div>
                        {% if results.mobile.viewport.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.viewport.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.mobile.viewport.score >= 90 else 'score-good' if results.mobile.viewport.score >= 70 else 'score-average' if results.mobile.viewport.score >= 50 else 'score-poor' }}">
                        {{ results.mobile.viewport.score }}
                    </div>
                </div>
                {% endif %}

                <!-- 响应式设计检查 -->
                {% if results.mobile.responsive %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>响应式设计</strong>
                        <div class="details">
                            检测到: {{ '是' if results.mobile.responsive.detected else '否' }}
                        </div>
                        {% if results.mobile.responsive.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.mobile.responsive.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.mobile.responsive.score >= 90 else 'score-good' if results.mobile.responsive.score >= 70 else 'score-average' if results.mobile.responsive.score >= 50 else 'score-poor' }}">
                        {{ results.mobile.responsive.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 安全检查 -->
        {% if results.security %}
        <div class="section">
            <div class="section-header">
                <h2>🔒 安全检查</h2>
            </div>
            <div class="section-content">
                <!-- HTTPS检查 -->
                {% if results.security.https %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>HTTPS加密</strong>
                        <div class="details">
                            启用: {{ '是' if results.security.https.enabled else '否' }}
                        </div>
                        {% if results.security.https.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.security.https.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.security.https.score >= 90 else 'score-good' if results.security.https.score >= 70 else 'score-average' if results.security.https.score >= 50 else 'score-poor' }}">
                        {{ results.security.https.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 内容分析 -->
        {% if results.content_analysis %}
        <div class="section">
            <div class="section-header">
                <h2>📝 内容分析</h2>
            </div>
            <div class="section-content">
                <!-- 内容长度检查 -->
                {% if results.content_analysis.word_count %}
                <div class="check-item">
                    <div class="check-name">
                        <strong>内容长度</strong>
                        <div class="details">
                            词数: {{ results.content_analysis.word_count.count }}
                        </div>
                        {% if results.content_analysis.word_count.recommendations %}
                        <div class="recommendations">
                            <h4>建议:</h4>
                            <ul>
                                {% for rec in results.content_analysis.word_count.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <div class="check-score {{ 'score-excellent' if results.content_analysis.word_count.score >= 90 else 'score-good' if results.content_analysis.word_count.score >= 70 else 'score-average' if results.content_analysis.word_count.score >= 50 else 'score-poor' }}">
                        {{ results.content_analysis.word_count.score }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 {{ config.company }} SEO审计工具生成</p>
            <p>生成时间: {{ report_date }}</p>
        </div>
    </div>
</body>
</html>
        """
