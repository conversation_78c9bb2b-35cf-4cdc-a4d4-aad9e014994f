# SEO自动化检查工具 (SMind)

一个专业的网站SEO自动化检查工具，能够对网站进行全面的SEO分析并生成美观的HTML报告。

## 功能特性

### 🔍 基础SEO检查
- **页面标题分析**: 检查标题长度、内容质量
- **Meta描述检查**: 验证描述长度和存在性
- **标题结构分析**: H1-H6标签的使用情况
- **图片优化检查**: Alt属性完整性检查
- **Meta关键词**: 关键词标签检查

### ⚙️ 技术SEO检查
- **Robots.txt检查**: 验证robots.txt文件存在性
- **XML网站地图**: 检查sitemap.xml可访问性
- **URL结构分析**: 友好URL结构检查
- **内部链接分析**: 内部链接数量和质量
- **外部链接检查**: 外部链接统计

### 🚀 性能分析
- **页面加载时间**: 测量页面响应时间
- **页面大小分析**: 检查页面资源大小
- **性能优化建议**: 基于测试结果的改进建议

### 📱 移动端友好性
- **Viewport设置**: 移动端viewport配置检查
- **响应式设计**: 媒体查询检测
- **移动端优化建议**: 移动端体验改进建议

### 🔒 安全检查
- **HTTPS检查**: SSL证书和加密连接验证
- **安全协议分析**: 安全相关配置检查

### 📝 内容分析
- **内容长度分析**: 页面内容词数统计
- **内容质量评估**: 基于长度的内容质量评分
- **重复内容检测**: 简单的重复内容检查

## 安装说明

### 环境要求
- Python 3.7+
- Chrome浏览器 (用于某些高级检查)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd smind
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **验证安装**
```bash
python main.py --help
```

## 使用方法

### 基本用法
```bash
python main.py https://example.com
```

### 指定输出文件
```bash
python main.py https://example.com -o my_seo_report.html
```

### 显示详细输出
```bash
python main.py https://example.com -v
```

### 完整命令选项
```bash
python main.py <URL> [选项]

位置参数:
  URL                   要检查的网站URL

可选参数:
  -h, --help           显示帮助信息
  -o, --output FILE    输出报告文件名 (默认: seo_report.html)
  -v, --verbose        显示详细输出
```

## 使用示例

### 检查公司官网
```bash
python main.py https://www.yourcompany.com -o company_seo_report.html -v
```

### 批量检查多个页面
```bash
# 创建一个简单的批处理脚本
python main.py https://www.example.com -o homepage_report.html
python main.py https://www.example.com/about -o about_report.html
python main.py https://www.example.com/products -o products_report.html
```

## 报告说明

生成的HTML报告包含以下部分：

### 📊 总体评分
- **A级 (90-100分)**: 优秀的SEO表现
- **B级 (80-89分)**: 良好的SEO表现
- **C级 (70-79分)**: 一般的SEO表现
- **D级 (60-69分)**: 需要改进的SEO表现
- **F级 (0-59分)**: 较差的SEO表现

### 📋 详细检查项目
每个检查项目都包含：
- **当前状态**: 检查项目的具体数值或状态
- **评分**: 0-100分的评分
- **建议**: 具体的改进建议
- **技术细节**: 相关的技术信息

## 配置说明

可以通过修改 `config.py` 文件来自定义检查参数：

### 评分权重配置
```python
SCORE_WEIGHTS = {
    'title': 15,           # 页面标题权重
    'meta_description': 10, # 元描述权重
    'headings': 10,        # 标题结构权重
    'images': 10,          # 图片优化权重
    'links': 10,           # 链接分析权重
    'page_speed': 15,      # 页面速度权重
    'mobile_friendly': 10, # 移动端友好权重
    'ssl': 5,              # SSL证书权重
    'content_length': 5,   # 内容长度权重
    'meta_keywords': 5,    # 关键词权重
    'robots_txt': 5        # robots.txt权重
}
```

### 内容标准配置
```python
MIN_CONTENT_LENGTH = 300      # 最小内容长度
OPTIMAL_CONTENT_LENGTH = 1000 # 最佳内容长度
MIN_TITLE_LENGTH = 30         # 最小标题长度
MAX_TITLE_LENGTH = 60         # 最大标题长度
MIN_DESCRIPTION_LENGTH = 120  # 最小描述长度
MAX_DESCRIPTION_LENGTH = 160  # 最大描述长度
```

## 项目结构

```
smind/
├── main.py                 # 主程序入口
├── seo_checker.py         # SEO检查核心逻辑
├── report_generator.py    # HTML报告生成器
├── config.py              # 配置文件
├── quick_check.py         # 快速检查工具
├── example_usage.py       # 使用示例
├── requirements.txt       # 依赖包列表
├── README.md             # 项目说明文档
├── 使用指南.md           # 详细使用指南
└── reports/             # 报告输出目录
    ├── static/          # 报告专用样式文件
    │   └── style.css    # CSS样式
    └── *.html          # 生成的SEO报告文件
```

### 报告文件管理
- 所有SEO报告自动保存在 `reports/` 目录下
- 报告包含独立的样式文件，便于分享和归档
- 智能命名：自动使用网站标题+时间戳命名文件
- 支持自定义报告文件名（使用 `-o` 参数）
- 自动清理文件名中的特殊字符，确保兼容性

## 常见问题

### Q: 检查失败，提示无法访问网站
A: 请检查：
- 网站URL是否正确
- 网站是否可以正常访问
- 网络连接是否正常
- 是否有防火墙阻止访问

### Q: 某些检查项目显示错误
A: 可能的原因：
- 网站响应时间过长
- 网站返回错误状态码
- 网站使用了特殊的防爬虫机制

### Q: 如何自定义检查标准
A: 修改 `config.py` 文件中的相关参数，如长度标准、权重配置等。

### Q: 报告中的建议如何理解
A: 每个建议都是基于SEO最佳实践制定的，建议按优先级逐步实施改进。

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查错误日志信息
3. 使用 `-v` 参数获取详细输出
4. 联系技术支持团队

## 更新日志

### v1.0.0 (当前版本)
- 初始版本发布
- 支持基础SEO检查
- 支持技术SEO分析
- 支持性能检查
- 支持移动端友好性检查
- 支持安全检查
- 支持HTML报告生成

## 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

---

**开发团队**: 测试工程师团队
**最后更新**: 2024年
**版本**: v1.0.0
